@extends('layouts.admin')

@section('title', 'Detail Peminjaman #' . $borrowing->id)
@section('page-title', 'Detail Peminjaman')

@section('breadcrumb')
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="{{ route('admin.borrowings.index') }}">Data Peminjaman</a></li>
        <li class="breadcrumb-item active" aria-current="page">Detail #{{ $borrowing->id }}</li>
    </ol>
@endsection

@section('admin-content')
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">Detail Peminjaman #{{ $borrowing->id }}</h5>
        <a href="{{ route('admin.borrowings.index') }}" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-left me-1"></i> <PERSON><PERSON><PERSON> ke Daftar
        </a>
    </div>
    <div class="card-body">
        <div class="row">
            {{-- Borrowing Details --}}
            <div class="col-md-6">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informasi Peminjaman</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <th style="width: 200px;">ID Peminjaman</th>
                                    <td>{{ $borrowing->id }}</td>
                                </tr>
                                <tr>
                                    <th>Tanggal Pinjam</th>
                                    <td>{{ $borrowing->borrowed_at->format('l, d F Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <th>Batas Pengembalian</th>
                                    <td>{{ $borrowing->due_date->format('l, d F Y') }}</td>
                                </tr>
                                <tr>
                                    <th>Tanggal Dikembalikan</th>
                                    <td>{{ $borrowing->returned_at ? $borrowing->returned_at->format('l, d F Y H:i') : '-' }}</td>
                                </tr>
                                <tr>
                                    <th>Status</th>
                                    <td>
                                        @if ($borrowing->status == 'returned')
                                            <span class="badge bg-success fs-6">Dikembalikan</span>
                                        @elseif ($borrowing->due_date->isPast() && $borrowing->status == 'borrowed')
                                            <span class="badge bg-danger fs-6">Terlambat</span>
                                        @else
                                            <span class="badge bg-warning text-dark fs-6">Dipinjam</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Denda</th>
                                    <td class="fw-bold">Rp {{ number_format($borrowing->fine ?? 0, 0, ',', '.') }}</td>
                                </tr>
                            </tbody>
                        </table>
                        @if($borrowing->status == 'borrowed')
                        <div class="d-grid mt-3">
                             <form action="{{ route('admin.borrowings.return', $borrowing->id) }}" method="POST" onsubmit="return confirm('Konfirmasi pengembalian buku ini?');">
                                @csrf
                                @method('PATCH')
                                <button class="btn btn-success w-100"><i class="fas fa-check-circle me-2"></i>Konfirmasi Pengembalian Buku</button>
                            </form>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            {{-- User & Book Details --}}
            <div class="col-md-6">
                {{-- User Details --}}
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-user me-2"></i>Informasi Peminjam</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless table-sm">
                            <tbody>
                                <tr>
                                    <th style="width: 120px;">Nama</th>
                                    <td>: {{ $borrowing->user->name }}</td>
                                </tr>
                                <tr>
                                    <th>Email</th>
                                    <td>: {{ $borrowing->user->email }}</td>
                                </tr>
                                 <tr>
                                    <th>NIM/NIP</th>
                                    <td>: {{ $borrowing->user->student_id }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                 {{-- Book Details --}}
                <div class="card shadow-sm">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0"><i class="fas fa-book me-2"></i>Informasi Buku</h6>
                    </div>
                     <div class="card-body">
                         <table class="table table-borderless table-sm">
                            <tbody>
                                <tr>
                                    <th style="width: 120px;">Judul</th>
                                    <td>: {{ $borrowing->book->title }}</td>
                                </tr>
                                <tr>
                                    <th>Pengarang</th>
                                    <td>: {{ $borrowing->book->author }}</td>
                                </tr>
                                 <tr>
                                    <th>Kategori</th>
                                    <td>: {{ $borrowing->book->category->name ?? '-' }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection