@extends('layouts.app')

@section('title', 'Dashboard - Perpustakaan Kampus')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">
                <i class="fas fa-tachometer-alt me-2"></i>
                Dashboard
            </h2>
            <p class="text-muted">Selamat datang, {{ auth()->user()->name }}!</p>
        </div>
    </div>

    <!-- Welcome Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="card-title">
                                <i class="fas fa-book-open me-2"></i>
                                Selamat Datang di Perpustakaan Digital Kampus
                            </h4>
                            <p class="card-text mb-0">
                                Je<PERSON>jahi koleksi buku digital kami dan nikmati kemudahan meminjam buku secara online.
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <i class="fas fa-graduation-cap fa-4x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <h5 class="mb-3">Aksi Cepat</h5>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-search fa-3x text-primary"></i>
                    </div>
                    <h6 class="card-title">Cari Buku</h6>
                    <p class="card-text text-muted small">Temukan buku yang Anda butuhkan</p>
                    <a href="#" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-search me-1"></i>Cari
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-book fa-3x text-success"></i>
                    </div>
                    <h6 class="card-title">Koleksi Buku</h6>
                    <p class="card-text text-muted small">Lihat semua buku tersedia</p>
                    <a href="#" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-book me-1"></i>Lihat
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-history fa-3x text-warning"></i>
                    </div>
                    <h6 class="card-title">Riwayat Peminjaman</h6>
                    <p class="card-text text-muted small">Lihat riwayat peminjaman Anda</p>
                    <a href="#" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-history me-1"></i>Riwayat
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-user fa-3x text-info"></i>
                    </div>
                    <h6 class="card-title">Profil</h6>
                    <p class="card-text text-muted small">Kelola informasi akun Anda</p>
                    <a href="#" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-user me-1"></i>Profil
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Overview -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Statistik Peminjaman
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary mb-1">0</h4>
                                <small class="text-muted">Sedang Dipinjam</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success mb-1">0</h4>
                            <small class="text-muted">Total Peminjaman</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Informasi Akun
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Email:</strong>
                        </div>
                        <div class="col-sm-6">
                            {{ auth()->user()->email }}
                        </div>
                    </div>
                    <hr class="my-2">
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Bergabung:</strong>
                        </div>
                        <div class="col-sm-6">
                            {{ auth()->user()->created_at->format('d M Y') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
