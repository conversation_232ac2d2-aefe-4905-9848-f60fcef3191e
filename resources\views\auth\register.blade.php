```blade
@extends('layouts.guest')

@section('title', 'Daftar - Perpustakaan Kampus')

@section('content')
<div class="card auth-card">
    <div class="card-body p-5">
        <div class="text-center mb-4">
            <h3 class="card-title text-primary">
                <i class="fas fa-user-plus me-2"></i>
                Daftar Akun Baru
            </h3>
            <p class="text-muted">Buat akun untuk mengakses perpus<PERSON>an</p>
        </div>

        <form method="POST" action="{{ route('register') }}">
            @csrf
            
            <div class="mb-3">
                <label for="name" class="form-label"><PERSON><PERSON></label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                           id="name" name="name" value="{{ old('name') }}" required>
                </div>
                @error('name')
                    <div class="invalid-feedback d-block">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label for="email" class="form-label">Email</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                           id="email" name="email" value="{{ old('email') }}" required>
                </div>
                @error('email')
                    <div class="invalid-feedback d-block">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label for="student_id" class="form-label">NIM/NIP</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                    <input type="text" class="form-control @error('student_id') is-invalid @enderror" 
                           id="student_id" name="student_id" value="{{ old('student_id') }}" required>
                </div>
                @error('student_id')
                    <div class="invalid-feedback d-block">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label for="password" class="form-label">Password</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                    <input type="password" class="form-control @error('password') is-invalid @enderror" 
                           id="password" name="password" required>
                </div>
                @error('password')
                    <div class="invalid-feedback d-block">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-3">
                <label for="password_confirmation" class="form-label">Konfirmasi Password</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                    <input type="password" class="form-control" 
                           id="password_confirmation" name="password_confirmation" required>
                </div>
            </div>

            <button type="submit" class="btn btn-primary w-100 mb-3">
                <i class="fas fa-user-plus me-2"></i>Daftar
            </button>
        </form>

        <div class="text-center">
            <p class="mb-0">Sudah punya akun? 
                <a href="{{ route('login') }}" class="text-primary">Masuk di sini</a>
            </p>
        </div>
    </div>
</div>
@endsection
```