```blade
@extends('layouts.app')

@section('title', 'Dashboard User')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">Dashboard</h2>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5 class="card-title">Buku Dipinjam</h5>
                            <h3 class="mb-0">{{ $borrowedBooks ?? 0 }}</h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-book fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5 class="card-title">Buku Dikembalikan</h5>
                            <h3 class="mb-0">{{ $returnedBooks ?? 0 }}</h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5 class="card-title">Denda</h5>
                            <h3 class="mb-0">Rp {{ number_format($totalFines ?? 0, 0, ',', '.') }}</h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5 class="card-title">Total Peminjaman</h5>
                            <h3 class="mb-0">{{ $totalBorrowings ?? 0 }}</h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-history fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Borrowings -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Peminjaman Terbaru</h5>
                </div>
                <div class="card-body">
                    @if(isset($recentBorrowings) && $recentBorrowings->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Judul Buku</th>
                                        <th>Tanggal Pinjam</th>
                                        <th>Batas Kembali</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentBorrowings as $borrowing)
                                    <tr>
                                        <td>{{ $borrowing->book->title }}</td>
                                        <td>{{ $borrowing->borrowed_at->format('d/m/Y') }}</td>
                                        <td>{{ $borrowing->due_date->format('d/m/Y') }}</td>
                                        <td>
                                            <span class="badge bg-{{ $borrowing->status == 'borrowed' ? 'warning' : 'success' }}">
                                                {{ ucfirst($borrowing->status) }}
                                            </span>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted">Belum ada peminjaman.</p>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Buku Populer</h5>
                </div>
                <div class="card-body">
                    @if(isset($popularBooks) && $popularBooks->count() > 0)
                        <div class="list-group list-group-flush">
                            @foreach($popularBooks as $book)
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ $book->title }}</h6>
                                    <small class="text-muted">{{ $book->author }}</small>
                                </div>
                                <span class="badge bg-primary rounded-pill">{{ $book->borrows_count }}</span>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-muted">Tidak ada data buku populer.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
```