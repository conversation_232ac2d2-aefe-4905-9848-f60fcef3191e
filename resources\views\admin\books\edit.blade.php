@extends('layouts.admin')

@section('title', 'Edit Buku')
@section('page-title', 'Edit Buku')
@section('breadcrumb')
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="{{ route('admin.books.index') }}">Buku</a></li>
        <li class="breadcrumb-item active" aria-current="page">Edit</li>
    </ol>
@endsection

@section('admin-content')
<div class="card">
    <div class="card-header">
        <h5 class="card-title">Form Edit Buku</h5>
    </div>
    <form action="{{ route('admin.books.update', $book->id) }}" method="POST">
        @csrf
        @method('PUT')
        <div class="card-body">
            @include('admin.books.partials.form-fields', ['book' => $book])
        </div>
        <div class="card-footer text-end">
            <a href="{{ route('admin.books.index') }}" class="btn btn-secondary">Batal</a>
            <button type="submit" class="btn btn-primary">Update</button>
        </div>
    </form>
</div>
@endsection