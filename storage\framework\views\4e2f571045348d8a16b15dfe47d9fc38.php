<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($book->title); ?> - <PERSON>pus<PERSON>kaan Kampus</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand" href="/dashboard">
                <i class="fas fa-book-open me-2"></i><PERSON>pus<PERSON>kaan Kampus
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard">Dashboard</a>
                <a class="nav-link" href="/books">Buku</a>
                <a class="nav-link" href="/my-borrowings">Peminjaman Saya</a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i><?php echo e(auth()->user()->name); ?>

                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <form action="/logout" method="POST" class="d-inline">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="dropdown-item">Logout</button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container py-4">
        <div class="row">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="/books">Buku</a></li>
                        <li class="breadcrumb-item active"><?php echo e($book->title); ?></li>
                    </ol>
                </nav>
            </div>
        </div>

        <?php if(session('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo e(session('success')); ?>

                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo e(session('error')); ?>

                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <?php if($book->cover_image): ?>
                            <img src="<?php echo e($book->cover_image); ?>" alt="<?php echo e($book->title); ?>" class="img-fluid mb-3" style="max-height: 300px;">
                        <?php else: ?>
                            <div class="bg-light p-5 mb-3" style="height: 300px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-book fa-4x text-muted"></i>
                            </div>
                        <?php endif; ?>
                        
                        <div class="d-grid gap-2">
                            <?php if($book->available_stock > 0): ?>
                                <form method="POST" action="/books/<?php echo e($book->id); ?>/borrow">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="btn btn-primary btn-lg w-100">
                                        <i class="fas fa-book me-2"></i>Pinjam Buku
                                    </button>
                                </form>
                            <?php else: ?>
                                <button class="btn btn-secondary btn-lg w-100" disabled>
                                    <i class="fas fa-times me-2"></i>Tidak Tersedia
                                </button>
                            <?php endif; ?>
                            <a href="/books" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Kembali ke Daftar
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title mb-0"><?php echo e($book->title); ?></h3>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Pengarang:</strong></div>
                            <div class="col-sm-9"><?php echo e($book->author); ?></div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>ISBN:</strong></div>
                            <div class="col-sm-9"><?php echo e($book->isbn); ?></div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Kategori:</strong></div>
                            <div class="col-sm-9">
                                <span class="badge bg-primary"><?php echo e($book->category->name); ?></span>
                            </div>
                        </div>
                        
                        <?php if($book->publisher): ?>
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Penerbit:</strong></div>
                            <div class="col-sm-9"><?php echo e($book->publisher); ?></div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if($book->publication_year): ?>
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Tahun Terbit:</strong></div>
                            <div class="col-sm-9"><?php echo e($book->publication_year); ?></div>
                        </div>
                        <?php endif; ?>
                        
                        <div class="row mb-3">
                            <div class="col-sm-3"><strong>Stok:</strong></div>
                            <div class="col-sm-9">
                                <span class="badge bg-<?php echo e($book->available_stock > 0 ? 'success' : 'danger'); ?>">
                                    <?php echo e($book->available_stock); ?> dari <?php echo e($book->stock); ?> tersedia
                                </span>
                            </div>
                        </div>
                        
                        <?php if($book->description): ?>
                        <div class="row">
                            <div class="col-12">
                                <h5>Deskripsi</h5>
                                <p class="text-muted"><?php echo e($book->description); ?></p>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Herd\dashboard\resources\views/books/show-simple.blade.php ENDPATH**/ ?>