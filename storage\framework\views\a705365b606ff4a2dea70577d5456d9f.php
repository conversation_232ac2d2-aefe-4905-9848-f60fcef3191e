<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - <PERSON><PERSON><PERSON></title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-dark">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card mt-5 border-danger">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <div class="bg-danger text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                <i class="fas fa-user-shield fa-2x"></i>
                            </div>
                            <h3 class="card-title text-danger">
                                Admin Login
                            </h3>
                            <p class="text-muted">Akses khusus untuk administrator sistem</p>
                        </div>

                        <?php if($errors->any()): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li><?php echo e($error); ?></li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <form method="POST" action="/admin/login">
                            <?php echo csrf_field(); ?>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Admin</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-danger text-white"><i class="fas fa-envelope"></i></span>
                                    <input type="email" class="form-control border-danger" id="email" name="email" value="<?php echo e(old('email')); ?>" placeholder="<EMAIL>" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-danger text-white"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control border-danger" id="password" name="password" required>
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                <label class="form-check-label" for="remember">Ingat saya</label>
                            </div>

                            <!-- Demo Login -->
                            <div class="alert alert-warning">
                                <strong>Akun Admin Tersedia:</strong><br>
                                <div class="row">
                                    <div class="col-6">
                                        <small>Demo Admin:</small><br>
                                        Email: <code><EMAIL></code><br>
                                        Password: <code>password</code><br>
                                        <button type="button" class="btn btn-sm btn-outline-warning mt-1" onclick="fillDemo()">
                                            <i class="fas fa-magic me-1"></i>Demo
                                        </button>
                                    </div>
                                    <div class="col-6">
                                        <small>Admin Utama:</small><br>
                                        Email: <code><EMAIL></code><br>
                                        Password: <code>12345678</code><br>
                                        <button type="button" class="btn btn-sm btn-outline-primary mt-1" onclick="fillMain()">
                                            <i class="fas fa-user-shield me-1"></i>Main
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-danger w-100 mb-3">
                                <i class="fas fa-sign-in-alt me-2"></i>Masuk sebagai Admin
                            </button>
                        </form>

                        <div class="text-center">
                            <p class="mb-0">Bukan admin? 
                                <a href="/login" class="text-primary">Login sebagai User</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function fillDemo() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'password';
        }

        function fillMain() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = '12345678';
        }
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Herd\dashboard\resources\views/auth/admin-login.blade.php ENDPATH**/ ?>