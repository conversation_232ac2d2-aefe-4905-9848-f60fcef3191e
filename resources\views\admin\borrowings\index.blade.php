@extends('layouts.admin')

@section('title', 'Data Peminjaman')
@section('page-title', 'Data Peminjaman')

@section('admin-content')
<div class="card">
    <div class="card-header">
        <h5 class="card-title">Daftar Se<PERSON></h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover table-bordered">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Peminjam</th>
                        <th>Judul Buku</th>
                        <th>Tgl Pinjam</th>
                        <th><PERSON><PERSON> Waktu</th>
                        <th>Status</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($borrowings as $borrowing)
                        <tr>
                            <td>{{ $borrowing->id }}</td>
                            <td>{{ $borrowing->user->name }}</td>
                            <td>{{ $borrowing->book->title }}</td>
                            <td>{{ $borrowing->borrowed_at->format('d M Y') }}</td>
                            <td>{{ $borrowing->due_date->format('d M Y') }}</td>
                            <td>
                                <span class="badge bg-{{ $borrowing->status == 'borrowed' ? 'warning' : 'success' }}">
                                    {{ ucfirst($borrowing->status) }}
                                </span>
                            </td>
                            <td>
                                @if($borrowing->status == 'borrowed')
                                    <form action="{{ route('admin.borrowings.return', $borrowing->id) }}" method="POST" onsubmit="return confirm('Konfirmasi pengembalian buku ini?');">
                                        @csrf
                                        @method('PATCH')
                                        <button class="btn btn-success btn-sm">Tandai Kembali</button>
                                    </form>
                                @else
                                    <a href="{{ route('admin.borrowings.show', $borrowing->id) }}" class="btn btn-info btn-sm">Detail</a>
                                @endif
                            </td>
                        </tr>
                    @empty
                        <tr><td colspan="7" class="text-center">Tidak ada data peminjaman.</td></tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        @if($borrowings->hasPages())
            <div class="d-flex justify-content-center mt-3">{{ $borrowings->links() }}</div>
        @endif
    </div>
</div>
@endsection