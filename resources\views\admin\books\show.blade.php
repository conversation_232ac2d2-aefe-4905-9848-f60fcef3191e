@extends('layouts.admin')

@section('title', 'Detail Buku: ' . $book->title)
@section('page-title', 'Detail Buku')

@section('breadcrumb')
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="{{ route('admin.books.index') }}">Manajemen Buku</a></li>
        <li class="breadcrumb-item active" aria-current="page">Detail</li>
    </ol>
@endsection

@section('admin-content')
<div class="row">
    {{-- Book Details Card --}}
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{{ $book->title }}</h5>
                <div>
                    <a href="{{ route('admin.books.edit', $book->id) }}" class="btn btn-warning btn-sm">
                        <i class="fas fa-edit me-1"></i> Edit
                    </a>
                    <form action="{{ route('admin.books.destroy', $book->id) }}" method="POST" class="d-inline" onsubmit="return confirm('Apakah Anda yakin ingin menghapus buku ini secara permanen?');">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger btn-sm">
                            <i class="fas fa-trash me-1"></i> Hapus
                        </button>
                    </form>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center">
                        <img src="https://placehold.co/300x450/764ba2/ffffff?text=Cover" class="img-fluid rounded shadow-sm mb-3" alt="Cover Buku {{ $book->title }}">
                    </div>
                    <div class="col-md-8">
                        <table class="table table-sm table-bordered">
                            <tbody>
                                <tr>
                                    <th style="width: 150px;">Pengarang</th>
                                    <td>{{ $book->author }}</td>
                                </tr>
                                <tr>
                                    <th>Kategori</th>
                                    <td>{{ $book->category->name ?? 'Tidak ada kategori' }}</td>
                                </tr>
                                <tr>
                                    <th>Tahun Terbit</th>
                                    <td>{{ $book->publication_year }}</td>
                                </tr>
                                <tr>
                                    <th>Stok</th>
                                    <td>
                                        <span class="badge bg-{{ $book->stock > 0 ? 'success' : 'danger' }}">
                                            {{ $book->stock }}
                                        </span>
                                    </td>
                                </tr>
                                 <tr>
                                    <th>Total Dipinjam</th>
                                    <td>{{ $book->borrows_count ?? $book->borrowings->count() }} kali</td>
                                </tr>
                            </tbody>
                        </table>
                        <h6 class="mt-4">Sinopsis</h6>
                        <p class="text-muted">{{ $book->synopsis }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Borrowing History Card --}}
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="fas fa-history me-2"></i>Riwayat Peminjaman</h5>
            </div>
            <div class="card-body">
                 @if(isset($book->borrowings) && $book->borrowings->count() > 0)
                    <div class="list-group list-group-flush">
                        @foreach($book->borrowings as $borrowing)
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ $borrowing->user->name }}</h6>
                                    <small>{{ $borrowing->borrowed_at->diffForHumans() }}</small>
                                </div>
                                <p class="mb-1">
                                    Status: 
                                    <span class="badge bg-{{ $borrowing->status == 'returned' ? 'success' : 'warning text-dark' }}">
                                        {{ ucfirst($borrowing->status) }}
                                    </span>
                                </p>
                            </div>
                        @endforeach
                    </div>
                 @else
                    <p class="text-center text-muted">Buku ini belum pernah dipinjam.</p>
                 @endif
            </div>
        </div>
    </div>
</div>
@endsection