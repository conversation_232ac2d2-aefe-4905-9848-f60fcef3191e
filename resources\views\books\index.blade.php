```blade
@extends('layouts.app')

@section('title', 'Daftar Buku')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Daftar Buku</h2>
                <a href="{{ route('books.search') }}" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>Pencarian Lanjutan
                </a>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="row mb-4">
        <div class="col-md-8">
            <form method="GET" action="{{ route('books.index') }}">
                <div class="input-group">
                    <input type="text" class="form-control" name="search" 
                           placeholder="Cari judul buku, pengarang, atau kategori..." 
                           value="{{ request('search') }}">
                    <button class="btn btn-outline-primary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
        <div class="col-md-4">
            <select class="form-select" name="category" onchange="this.form.submit()">
                <option value="">Semua Kategori</option>
                @if(isset($categories))
                    @foreach($categories as $category)
                        <option value="{{ $category->id }}" 
                                {{ request('category') == $category->id ? 'selected' : '' }}>
                            {{ $category->name }}
                        </option>
                    @endforeach
                @endif
            </select>
        </div>
    </div>

    <!-- Books Grid -->
    <div class="row">
        @if(isset($books) && $books->count() > 0)
            @foreach($books as $book)
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">{{ $book->title }}</h5>
                        <p class="card-text">
                            <strong>Pengarang:</strong> {{ $book->author }}<br>
                            <strong>Kategori:</strong> {{ $book->category->name ?? '-' }}<br>
                            <strong>Tahun Terbit:</strong> {{ $book->publication_year }}<br>
                            <strong>Stok:</strong> 
                            <span class="badge bg-{{ $book->stock > 0 ? 'success' : 'danger' }}">
                                {{ $book->stock }} tersedia
                            </span>
                        </p>
                        <p class="card-text">
                            {{ Str::limit($book->synopsis, 100) }}
                        </p>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('books.show', $book->id) }}" class="btn btn-info btn-sm">
                                <i class="fas fa-eye me-1"></i>Detail
                            </a>
                            @if($book->stock > 0)
                                <form method="POST" action="{{ route('borrowings.store') }}" class="d-inline">
                                    @csrf
                                    <input type="hidden" name="book_id" value="{{ $book->id }}">
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="fas fa-book me-1"></i>Pinjam
                                    </button>
                                </form>
                            @else
                                <button class="btn btn-secondary btn-sm" disabled>
                                    <i class="fas fa-times me-1"></i>Tidak Tersedia
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        @else
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-book fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">Tidak ada buku ditemukan</h4>
                    <p class="text-muted">Coba ubah kata kunci pencarian atau filter kategori.</p>
                </div>
            </div>
        @endif
    </div>

    <!-- Pagination -->
    @if(isset($books) && $books->hasPages())
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-center">
                    {{ $books->links() }}
                </div>
            </div>
        </div>
    @endif
</div>
@endsection
```