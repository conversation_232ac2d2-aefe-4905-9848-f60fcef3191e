@extends('layouts.app')

@section('title', 'Detail Buku - ' . $book->title)

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="mb-0">{{ $book->title }}</h2>
                        <a href="{{ url()->previous() }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Kembali
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <table class="table table-bordered">
                                <tbody>
                                    <tr>
                                        <th style="width: 200px;">Pengarang</th>
                                        <td>{{ $book->author }}</td>
                                    </tr>
                                    <tr>
                                        <th><PERSON><PERSON><PERSON></th>
                                        <td>{{ $book->category->name ?? '-' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Tahun Terbit</th>
                                        <td>{{ $book->publication_year }}</td>
                                    </tr>
                                    <tr>
                                        <th>Stok Tersedia</th>
                                        <td>
                                            <span class="badge bg-{{ $book->stock > 0 ? 'success' : 'danger' }}">
                                                {{ $book->stock }}
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <h5 class="mt-4">Sinopsis</h5>
                            <p>{{ $book->synopsis }}</p>
                        </div>
                        <div class="col-md-4">
                            {{-- Placeholder for Book Cover Image --}}
                            <img src="https://placehold.co/300x450/667eea/ffffff?text=Cover+Buku" class="img-fluid rounded shadow" alt="Cover Buku {{ $book->title }}">
                        </div>
                    </div>
                </div>
                <div class="card-footer text-end">
                    @if($book->stock > 0)
                        <form method="POST" action="{{ route('borrowings.store') }}" class="d-inline">
                            @csrf
                            <input type="hidden" name="book_id" value="{{ $book->id }}">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-book me-2"></i>Pinjam Buku Ini
                            </button>
                        </form>
                    @else
                        <button class="btn btn-secondary" disabled>
                            <i class="fas fa-times me-2"></i>Stok Habis
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection