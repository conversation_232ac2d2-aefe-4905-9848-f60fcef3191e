@extends('layouts.app')

@section('title', 'Buku yang Sedang Dipinjam')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">Buku yang Sedang Anda Pinjam</h2>
            @if(session('success'))
                <div class="alert alert-success">{{ session('success') }}</div>
            @endif
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Judul Buku</th>
                                    <th>Tanggal Pinjam</th>
                                    <th><PERSON>as Pengembalian</th>
                                    <th>Keterangan</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($borrowings as $borrowing)
                                    <tr class="{{ $borrowing->due_date->isPast() ? 'table-danger' : '' }}">
                                        <td>{{ $borrowing->book->title }}</td>
                                        <td>{{ $borrowing->borrowed_at->format('d M Y') }}</td>
                                        <td>{{ $borrowing->due_date->format('d M Y') }}</td>
                                        <td>
                                            @if ($borrowing->due_date->isPast())
                                                <span class="badge bg-danger">Terlambat {{ $borrowing->due_date->diffInDays(now()) }} hari</span>
                                            @else
                                                Sisa waktu {{ now()->diffInDays($borrowing->due_date) }} hari
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="text-center">Anda tidak sedang meminjam buku.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection