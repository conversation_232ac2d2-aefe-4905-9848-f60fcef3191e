<div class="card">
    <div class="card-header">
        <h5 class="card-title"><PERSON></h5>
    </div>
    <div class="card-body">
        <form action="{{ route('admin.categories.update', $categoryToEdit->id) }}" method="POST">
            @csrf
            @method('PUT')
            <div class="mb-3">
                <label for="name" class="form-label"><PERSON><PERSON></label>
                <input type="text" name="name" id="name" class="form-control @error('name') is-invalid @enderror" value="{{ old('name', $categoryToEdit->name) }}" required>
                @error('name') <div class="invalid-feedback">{{ $message }}</div> @enderror
            </div>
            <a href="{{ route('admin.categories.index') }}" class="btn btn-secondary">Batal</a>
            <button type="submit" class="btn btn-primary">Update</button>
        </form>
    </div>
</div>