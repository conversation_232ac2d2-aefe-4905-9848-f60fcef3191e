<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Perpustakaan <PERSON>')</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .sidebar {
            min-height: calc(100vh - 56px);
        }
    </style>
    @stack('styles')
</head>
<body>
    @include('components.navbar')
    
    <div class="container-fluid">
        <div class="row">
            @if(Auth::check())
                <div class="col-md-2 p-0">
                    @include('components.sidebar')
                </div>
                <div class="col-md-10">
                    @include('components.notification')
                    @yield('content')
                </div>
            @else
                <div class="col-12">
                    @include('components.notification')
                    @yield('content')
                </div>
            @endif
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    @stack('scripts')
</body>
</html>
```

### resources/views/layouts/admin.blade.php
```blade
@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>@yield('page-title', 'Admin Dashboard')</h2>
                <div class="breadcrumb-nav">
                    @yield('breadcrumb')
                </div>
            </div>
            
            @yield('admin-content')
        </div>
    </div>
</div>
@endsection
```