@extends('layouts.app')

@section('title', 'Pencarian Lanjutan')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h2><i class="fas fa-search me-2"></i>Pencarian Lanjutan</h2>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('books.index') }}">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="title" class="form-label">Judul Buku</label>
                                <input type="text" class="form-control" id="title" name="title" placeholder="Masukkan judul buku">
                            </div>
                            <div class="col-md-6">
                                <label for="author" class="form-label">Pengarang</label>
                                <input type="text" class="form-control" id="author" name="author" placeholder="Masukkan nama pengarang">
                            </div>
                            <div class="col-md-6">
                                <label for="category" class="form-label">Kategori</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="">Pilih Kategori</option>
                                    @if(isset($categories))
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}">{{ $category->name }}</option>
                                        @endforeach
                                    @endif
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="publication_year" class="form-label">Tahun Terbit</label>
                                <input type="number" class="form-control" id="publication_year" name="publication_year" placeholder="Contoh: 2023">
                            </div>
                        </div>
                        <div class="mt-4 text-end">
                            <a href="{{ route('books.index') }}" class="btn btn-secondary">Reset</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Cari Buku
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection