@extends('layouts.app')

@section('title', 'Riwayat Peminjaman')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4"><i class="fas fa-history me-2"></i>Riwayat Peminjaman Anda</h2>
            <div class="card">
                <div class="card-header">
                    <p class="card-title mb-0">Daftar semua buku yang pernah dan sedang Anda pinjam.</p>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>Judul Buku</th>
                                    <th>Tanggal Pinjam</th>
                                    <th>Batas Waktu</th>
                                    <th>Tanggal Kembali</th>
                                    <th>Status</th>
                                    <th>Denda</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($borrowings as $borrowing)
                                    <tr>
                                        <td>{{ $loop->iteration + $borrowings->firstItem() - 1 }}</td>
                                        <td>{{ $borrowing->book->title }}</td>
                                        <td>{{ $borrowing->borrowed_at->format('d M Y') }}</td>
                                        <td>{{ $borrowing->due_date->format('d M Y') }}</td>
                                        <td>{{ $borrowing->returned_at ? $borrowing->returned_at->format('d M Y') : '-' }}</td>
                                        <td>
                                            @if ($borrowing->status == 'returned')
                                                <span class="badge bg-success">Dikembalikan</span>
                                            @elseif ($borrowing->due_date->isPast() && $borrowing->status == 'borrowed')
                                                <span class="badge bg-danger">Terlambat</span>
                                            @else
                                                <span class="badge bg-warning text-dark">Dipinjam</span>
                                            @endif
                                        </td>
                                        <td>Rp {{ number_format($borrowing->fine ?? 0, 0, ',', '.') }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <p class="text-muted mb-0">Anda belum memiliki riwayat peminjaman.</p>
                                            <a href="{{ route('books.index') }}" class="btn btn-primary btn-sm mt-2">Cari Buku Sekarang</a>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    @if(isset($borrowings) && $borrowings->hasPages())
                        <div class="d-flex justify-content-center mt-3">
                            {{ $borrowings->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection