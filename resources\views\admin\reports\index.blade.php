@extends('layouts.admin')

@section('title', 'Laporan Peminjaman')
@section('page-title', 'Laporan Peminjaman')

@section('admin-content')
<div class="card">
    <div class="card-header">
        <h5 class="card-title">Filter Laporan</h5>
    </div>
    <div class="card-body">
        <form action="{{ route('admin.reports.generate') }}" method="GET">
            <div class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">Tanggal Mulai</label>
                    <input type="date" name="start_date" id="start_date" class="form-control" value="{{ request('start_date') }}">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">Tanggal Selesai</label>
                    <input type="date" name="end_date" id="end_date" class="form-control" value="{{ request('end_date') }}">
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary w-100">Tampilkan Laporan</button>
                </div>
            </div>
        </form>
    </div>
</div>

@if(isset($reportData))
<div class="card mt-4">
    <div class="card-header d-flex justify-content-between">
        <h5 class="card-title">Hasil Laporan</h5>
        <div>
            <a href="{{ route('admin.reports.export', request()->all()) }}?format=pdf" class="btn btn-danger btn-sm"><i class="fas fa-file-pdf"></i> Export PDF</a>
            <a href="{{ route('admin.reports.export', request()->all()) }}?format=excel" class="btn btn-success btn-sm"><i class="fas fa-file-excel"></i> Export Excel</a>
        </div>
    </div>
    <div class="card-body">
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>#</th><th>Peminjam</th><th>Buku</th><th>Tgl Pinjam</th><th>Tgl Kembali</th><th>Status</th>
                </tr>
            </thead>
            <tbody>
                @forelse($reportData as $data)
                <tr>
                    <td>{{ $loop->iteration }}</td>
                    <td>{{ $data->user->name }}</td>
                    <td>{{ $data->book->title }}</td>
                    <td>{{ $data->borrowed_at->format('d/m/Y') }}</td>
                    <td>{{ $data->returned_at ? $data->returned_at->format('d/m/Y') : '-' }}</td>
                    <td>{{ ucfirst($data->status) }}</td>
                </tr>
                @empty
                <tr><td colspan="6" class="text-center">Tidak ada data untuk periode yang dipilih.</td></tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>
@endif
@endsection